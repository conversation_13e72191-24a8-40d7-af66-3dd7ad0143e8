import { z } from "zod";
import { tool } from "@langchain/core/tools";

export interface InventorySearchArgs {
    id: string;
}

const inventoryImageSenderSchema = z.object({
    id: z.string().describe("The exact numeric ID of the product to send the image. Must be a valid inventory ID that was returned from inventory_retriever. Do not make up IDs or use arbitrary numbers."),
});

export const createInventoryImageSenderTool = (
    fn: (id: string) => Promise<boolean>,
) =>
    tool(
        async (input) => {
            console.log(`[InventoryImageSender] Tool called with input:`, input);

            // Validate ID is a number
            const inventoryId = parseInt(input.id);
            if (isNaN(inventoryId)) {
                console.log(`[InventoryImageSender] Invalid ID provided: ${input.id}`);
                return "Error: Invalid inventory ID. Please provide a valid numeric ID from the inventory_retriever results.";
            }

            console.log(`[InventoryImageSender] Calling image sender function with ID: ${input.id}`);
            const result = await fn(input.id);

            console.log(`[InventoryImageSender] Image sender function returned: ${result}`);

            if (result) {
                // Return an empty string to prevent the AI from generating a redundant confirmation message
                // The image itself serves as the response to the user
                return "";
            } else {
                return "Could not find or send the image. Please verify the inventory ID is correct and exists in the inventory.";
            }
        },
        {
            name: "inventoryImageSender",
            description: "Sends the inventory image for a specific product ID. ONLY use this tool with valid inventory IDs that were returned from the inventory_retriever tool. Do not make up IDs or use arbitrary numbers. After sending an image, do NOT send any confirmation message - the image itself is the response. Do NOT assume what product it was - refer back to the inventory_retriever results to confirm the product details.",
            schema: inventoryImageSenderSchema,
        },
    );
