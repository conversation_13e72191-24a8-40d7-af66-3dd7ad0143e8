/**
 * Utility functions for formatting inventory data in the chatbot
 */

import { LANGUAGE } from "../../../base/types/typing";
import { getCurrentLanguage } from "../../utils/language";
import { formatPrice } from "../../utils/text-formatter";

/**
 * Format inventory items for display based on the current language
 * @param inventoryItems The inventory items to format
 * @returns Formatted inventory text
 */
export function formatInventoryItems(inventoryItems: any[]): string {
    // Get current language
    const currentLanguage = getCurrentLanguage();

    console.log(`[InventoryFormatter] Formatting ${inventoryItems?.length || 0} inventory items for language: ${currentLanguage}`);

    if (!inventoryItems || inventoryItems.length === 0) {
        return currentLanguage === LANGUAGE.ENGLISH
            ? "No products found matching your criteria."
            : "محصولی با معیارهای مورد نظر شما یافت نشد.";
    }

    // Start with the header
    let response = currentLanguage === LANGUAGE.ENGLISH
        ? "We have the following products in our store:\n\n"
        : "در فروشگاه ما محصولات زیر موجود هستن:\n\n";

    // Format each inventory item
    inventoryItems.forEach((item, index) => {
        console.log(`[InventoryFormatter] Formatting item ${index}: ID=${item.id}, name=${item.name}, price=${item.price}`);

        // Add item number and name
        response += currentLanguage === LANGUAGE.ENGLISH
            ? `${index + 1}. ${item.name}\n`
            : `${index + 1}. ${item.name}\n`;

        // Add price if available
        if (item.price) {
            const formattedPrice = formatPrice(item.price, currentLanguage);
            response += currentLanguage === LANGUAGE.ENGLISH
                ? `   - Price: ${formattedPrice}\n`
                : `   - قیمت: ${formattedPrice}\n`;
        }

        // Add attributes if available
        if (item.attributes && item.attributes.length > 0) {
            item.attributes.forEach((attr: { key: string; value: string }) => {
                response += `   - ${attr.key}: ${attr.value}\n`;
            });
        }

        // Add a blank line between items (except after the last item)
        if (index < inventoryItems.length - 1) {
            response += "\n";
        }
    });

    console.log(`[InventoryFormatter] Final formatted response length: ${response.length}`);
    return response;
}

/**
 * Process the raw inventory retriever results to ensure proper formatting
 * @param rawResults The raw results from the inventory_retriever tool
 * @returns Properly formatted inventory text
 */
export function processInventoryResults(rawResults: string): string {
    try {
        console.log(`[InventoryFormatter] Processing raw results: ${rawResults.substring(0, 300)}...`);

        // Try to parse the results as JSON
        let inventoryItems;
        try {
            inventoryItems = JSON.parse(rawResults);
            console.log(`[InventoryFormatter] Successfully parsed JSON, type: ${typeof inventoryItems}, isArray: ${Array.isArray(inventoryItems)}`);
        } catch (e) {
            // If it's not valid JSON, try to extract and format the text directly
            console.log("[InventoryFormatter] Could not parse inventory results as JSON, attempting to format text directly");

            // Get current language
            const currentLanguage = getCurrentLanguage();

            // If we're in English mode, replace "Toman" with "$" in the text
            if (currentLanguage === LANGUAGE.ENGLISH) {
                // Replace price patterns like "X.XX Toman" or "X,XXX Toman" with "$X.XX"
                let formattedText = rawResults.replace(/(\d+(?:[.,]\d+)?)\s*(?:Toman|تومان)/gi, (_, price) => {
                    // Clean the price and convert to a number
                    const cleanPrice = price.replace(/,/g, '');
                    const numericPrice = parseFloat(cleanPrice);

                    // Format as dollar
                    return `$${numericPrice.toFixed(2)}`;
                });

                return formattedText;
            }

            return rawResults;
        }

        // If it's an array, format it
        if (Array.isArray(inventoryItems)) {
            console.log(`[InventoryFormatter] Processing array of ${inventoryItems.length} items`);
            inventoryItems.forEach((item, index) => {
                console.log(`[InventoryFormatter] Item ${index}: ID=${item.id}, name=${item.name}`);
            });
            return formatInventoryItems(inventoryItems);
        }

        // If it has a 'documents' property that's an array, format that
        if (inventoryItems.documents && Array.isArray(inventoryItems.documents)) {
            console.log(`[InventoryFormatter] Processing documents array of ${inventoryItems.documents.length} items`);
            inventoryItems.documents.forEach((item: any, index: number) => {
                console.log(`[InventoryFormatter] Document ${index}: ID=${item.id}, name=${item.name}`);
            });
            return formatInventoryItems(inventoryItems.documents);
        }

        // Otherwise, return the raw results
        console.log(`[InventoryFormatter] Returning raw results as-is`);
        return rawResults;
    } catch (error) {
        console.error("[InventoryFormatter] Error processing inventory results:", error);
        return rawResults;
    }
}
