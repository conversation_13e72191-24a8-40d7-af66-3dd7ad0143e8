export const resources = {
    en: {
        errors: {
            pgError: "PG error",
            duplicateColumn: "{{column}} already exists",
            dependencyExists: "Some {{table}}s have dependencies to this item.",
            validationError: "Validation error",
            invalidInterval: "Invalid interval specified",
            badRequest: "Bad request",
            invalidCondition: "Invalid condition",
            wrongOtp: "Wrong OTP",
            invalidFileName: "File name must be valid",
            otpExpired: "OTP Expired",
            internalError: "Something went wrong",
            unauthorized: "Unauthorized",
            invalidCredentials: "Invalid credentials",
            notFound: "Not found",
            resourceNotFound: "{{resource}} not found",
            cannotDuplicate: "Can not duplicate any more",
            cannotDuplicateResource:
                "Can not duplicate this {{resource}} with this {{uniqueColumn}} any more",
            forbidden: "Forbidden",
            tokenExpired: "Token Expired",
            deleteDependencyError: "can not delete because of dependency",
            deleteDependencyErrorResource:
                "can not delete because dependency to a {{resource}}",
            alreadyExists: "Already exists",
            usernameAlreadyExist:
                "{{resource}} with this username already exists.",
            emailAlreadyExist: "{{resource}} with this email already exists.",
            missingArguments:
                "Please provide username, password, and email as command-line arguments",
            incorrectLoginMethod: "Incorrect login method",
            instagramAlreadyDisconnected: "Instagram account is already disconnected",
            Automation: "Automation",
            Condition: "Condition",
            Action: "Action",
            User: "User",
            Client: "Client",
            Category: "Category",
            Inventories: "Inventories",
            Chat: "Chat",
            Website: "Website",
            UserWebsite: "UserWebsite",
            Channel: "Channel",
            Order: "Order",
            Rate: "Rate",
            StaticServer: "StaticServer",
            Admin: "Admin",
        },
    },
    fa: {
        errors: {
            pgError: "خطای پایگاه داده",
            duplicateColumn: "{{column}} از قبل وجود دارد",
            dependencyExists: "برخی از {{table}}ها به این مورد وابسته هستند.",
            validationError: "خطای اعتبارسنجی",
            invalidInterval: "بازه زمانی نامعتبر مشخص شده است",
            badRequest: "درخواست نامعتبر",
            invalidCondition: "شرط نامعتبر",
            wrongOtp: "رمز یکبار مصرف اشتباه است",
            invalidFileName: "نام فایل باید معتبر باشد",
            otpExpired: "رمز یکبار مصرف منقضی شده است",
            internalError: "خطایی رخ داده است",
            unauthorized: "عدم احراز هویت",
            invalidCredentials: "اطلاعات ورود نامعتبر است",
            notFound: "یافت نشد",
            resourceNotFound: "{{resource}} یافت نشد",
            cannotDuplicate: "امکان تکرار بیشتر وجود ندارد",
            cannotDuplicateResource:
                "امکان تکرار این {{resource}} با این {{uniqueColumn}} وجود ندارد",
            forbidden: "دسترسی ممنوع",
            tokenExpired: "توکن منقضی شده است",
            deleteDependencyError: "به دلیل وابستگی امکان حذف وجود ندارد",
            deleteDependencyErrorResource:
                "به دلیل وابستگی به {{resource}} امکان حذف وجود ندارد",
            alreadyExists: "از قبل وجود دارد",
            usernameAlreadyExist:
                "{{resource}} با این نام کاربری از قبل وجود دارد.",
            emailAlreadyExist: "{{resource}} با این ایمیل از قبل وجود دارد.",
            missingArguments:
                "لطفا نام کاربری، رمز عبور و ایمیل را به عنوان آرگومان های کامند لاین ارائه دهید",
            incorrectLoginMethod: "روش ورود نادرست است",
            instagramAlreadyDisconnected: "حساب اینستاگرام از قبل قطع شده است",
            Automation: "اتومیشن",
            Condition: "کاندیشن",
            Action: "اکشن",
            User: "کاربر",
            Client: "مشتری",
            Category: "دسته بندی",
            Inventories: "محصولات",
            Chat: "چت",
            Website: "وب سایت",
            UserWebsite: "کاربر وبسایت",
            Channel: "کانال",
            Order: "سفارش",
            Rate: "درصد",
            StaticServer: "استاتیک سرور",
            Admin: "مدیر",
        },
    },
} as const;
