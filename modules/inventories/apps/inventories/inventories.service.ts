import { inject, injectable, registry } from "tsyringe";
import InventoryRepo from "./inventories.repo";
import {
    AutomaticInventoryDto,
    EditStockDto,
    Inventory,
    InventoryItemsDto,
    SearchInventoryDto,
} from "./types";
import { errors, utils } from "../../../common";
import { normalizeData } from "../../../common/lib/utils/data-normalizer";
import CategoryService from "../../../categories/apps/categories/categories.service";
import {
    getInventoriesSerializer,
    getMatchingInventoriesSerializer,
} from "./responses";
import AttributeService from "../attributes/attribute.service";
import { EntityManager } from "typeorm";
import UserWebsiteService from "../../../user-websites/apps/user-websites/user-website.service";
import Logger from "../../../common/lib/metrics/logger";
import {
    editInventoryLog,
    getInventoriesLog,
    inventoryCreationLog,
    inventoryNotFoundErrorLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";
import { BaseVectorStore } from "../../../common/lib/chatbot/vector-stores/base-vector-store";
import { registries } from "./registries";
import { LANGUAGE, VECTOR_TOKEN_NAME } from "../../../common/base/types/typing";
import { getCurrentLanguage } from "../../../common/lib/utils/language";

@registry(registries)
@injectable()
export default class InventoryService {
    constructor(
        private _repo: InventoryRepo,
        private _category: CategoryService,
        private _attributeService: AttributeService,
        private _userWebsiteService: UserWebsiteService,
        @inject(VECTOR_TOKEN_NAME.INVENTORY_VECTOR_STORE)
        private _vectorStore: BaseVectorStore,
    ) {}

    getSalesAndProfit = async (
        parsedQuery: Partial<Express.Query>,
        user: Express.User,
    ) => {
        const { filter } = parsedQuery;
        const { id: userId } = user;

        if (utils.isNil(filter)) {
            throw new errors.BadRequestError();
        }

        const { from, to } = filter;
        const { startDate, endDate } = this.parseAndValidateInputs(from, to);

        const salesData = await this._repo.getSalesAndProfitData(
            startDate,
            endDate,
            userId,
        );

        return {
            data: salesData,
            metadata: { from: startDate, to: endDate },
        };
    };

    getBestSellingProducts = async (parsedQuery: Partial<Express.Query>) => {
        const { filter, pageSize } = parsedQuery;
        if (utils.isNil(filter)) throw new errors.BadRequestError();

        const { from, to } = filter;
        const { startDate, endDate, validLimit } = this.parseAndValidateInputs(
            from,
            to,
            pageSize,
        );

        const bestSellers = await this._repo.getBestSellingProducts(
            startDate,
            endDate,
            validLimit,
        );

        return {
            data: bestSellers,
            metadata: { from: startDate, to: endDate },
        };
    };

    private parseAndValidateInputs = (
        from?: string,
        to?: string,
        pageSize?: string | number,
    ) => {
        if (utils.isNil(from) || utils.isNil(to))
            throw new errors.BadRequestError();

        const startDate = new Date(from);
        const endDate = new Date(to);
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime()))
            throw new errors.BadRequestError();

        const parsedLimit = parseInt(pageSize as string, 10);
        const validLimit =
            !isNaN(parsedLimit) && parsedLimit > 0 ? parsedLimit : 10;

        return { startDate, endDate, validLimit };
    };

    //TODO description for english version should be changed !
    private _generateDescription = (
        inventory: Inventory,
        categoryName: string,
    ) => {
        // Get current language
        const currentLanguage = getCurrentLanguage();

        if (currentLanguage === LANGUAGE.ENGLISH) {
            // English description
            let description = `Product ${inventory.name} (id: ${inventory.id}) in category ${categoryName} with price $${inventory.price}`;
            if (utils.isNotNil(inventory.attributes)) {
                description =
                    description +
                    " " +
                    `and attributes ${inventory.attributes.map(({ key, value }) => `${key}: ${value}`).join(", ")}`;
            }
            return description;
        } else {
            // Persian description
            let description = `محصول ${inventory.name} (id: ${inventory.id}) در دسته بندی ${categoryName} با قیمت ${inventory.price} تومان`;
            if (utils.isNotNil(inventory.attributes)) {
                description =
                    description +
                    " " +
                    `و ویژگی‌‌های ${inventory.attributes.map(({ key, value }) => `${key}: ${value}`).join(",")}`;
            }
            return description;
        }
    };

    private _generateAttributesVector = (
        categoryName: string | undefined,
        productName: string,
        attributes?: { key: string; value: string }[],
    ) => {
        return (
            (categoryName ?? "") +
            " " +
            productName +
            " " +
            (attributes?.map(({ value }) => value).join(" ") ?? "")
        );
    };

    private _indexInventoryInElastic = async (
        inventory: Inventory,
        categoryName: string,
    ) => {
        const description = this._generateDescription(inventory, categoryName);

        await this._vectorStore.index‌BySpecificId([
            {
                pageContent: description,
                metadata: { userId: inventory.userId },
                id: inventory.id.toString(),
            },
        ]);
    };

    addAutomaticInventory = async (args: AutomaticInventoryDto) => {
        // Normalize inventory data
        const normalizedArgs = normalizeData(args, {
            type: 'object',
            fieldOptions: {
                name: { type: 'text' },
                price: { type: 'price' },
                cost: { type: 'price' },
                total: { type: 'number' },
                reserved: { type: 'number' },
                url: { type: 'url' },
                attributes: { type: 'array' }
            }
        });

        // Check if attributes is null, undefined, or empty array
        if (utils.isNotNil(normalizedArgs.attributes) && normalizedArgs.attributes.length === 0) {
            Logger.error("Invalid attributes provided", {
                action: "addAutomaticInventory",
                statusCode: 400,
                error: "Empty attributes array is not allowed",
            });
            throw new errors.BadRequestError();
        }

        await this._repo.runTransaction(async (manager) => {
            let inventory = await this._repo.findOneByQuery(
                {
                    websiteId: normalizedArgs.websiteId,
                    referenceId: normalizedArgs.referenceId,
                    sku: normalizedArgs.sku,
                },
                [],
                { manager },
            );
            if (utils.isNil(inventory)) {
                inventory = await this._repo.create(
                    {
                        ...normalizedArgs,
                        isAutomated: true,
                    },
                    { manager },
                );
            }

            if (utils.isNotNil(normalizedArgs.attributes) && normalizedArgs.attributes.length > 0) {
                await this._attributeService.resetAttributes(
                    inventory.id,
                    normalizedArgs.attributes,
                    manager,
                );
            }

            //!! Note: Elasticsearch indexing is not implemented for automatic inventory
        });
    };

    addInventory = async (args: InventoryItemsDto, user: Express.User) => {
        const { id: userId } = user;

        // Normalize inventory data
        const normalizedArgs = normalizeData(args, {
            type: 'object',
            fieldOptions: {
                name: { type: 'text' },
                price: { type: 'price' },
                cost: { type: 'price' },
                total: { type: 'number' },
                reserved: { type: 'number' },
                url: { type: 'url' },
                attributes: { type: 'array' }
            }
        });
        const { categoryId, attributes, ...inventory } = normalizedArgs;

        // Check if attributes is null, undefined, or empty array
        if (utils.isNotNil(attributes) && attributes.length === 0) {
            Logger.error("Invalid attributes provided", {
                userId,
                action: "addInventory",
                statusCode: 400,
                error: "Empty attributes array is not allowed",
            });
            throw new errors.BadRequestError();
        }

        let category = null;
        if (utils.isNotNil(categoryId)) {
            category = await this._category.getCategoryOfUser(
                categoryId,
                userId,
            );
        }

        // Subscription check code removed - implement if needed

        // Generate attributes vector for search
        const attributesVector = this._generateAttributesVector(
            category?.name ?? "",
            normalizedArgs.name,
            attributes,
        );

        const createdInventory = await this._repo.runTransaction(
            async (manager) => {
                const createdInventory = await this._repo.create(
                    {
                        userId,
                        ...inventory,
                        ...(utils.isNotNil(categoryId) && { categoryId }),
                        attributesVector,
                    },
                    { manager },
                );

                if (utils.isNotNil(attributes) && attributes.length > 0) {
                    await this._attributeService.resetAttributes(
                        createdInventory.id,
                        attributes,
                        manager,
                    );
                }

                // Load the attributes
                const inventoryWithAttributes = await this._getInventoryWithAttributesInTransaction(
                    createdInventory.id,
                    userId,
                    manager
                );

                // Index in Elasticsearch
                await this._indexInventoryInElastic(
                    inventoryWithAttributes!,
                    category?.name ?? "نامشخص",
                );

                return inventoryWithAttributes!;
            },
        );

        inventoryCreationLog.inc();
        Logger.info("successfully create inventory", {
            userId,
            action: "addInventory",
            inventoryId: createdInventory.id,
        });
    };

    getInventories = async (
        user: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = user;

        const selectedWebsites =
            await this._userWebsiteService.getSelectedWebsitesOfUser(userId);

        const selectedWebsiteIds = selectedWebsites.map(
            ({ websiteId }) => websiteId,
        );

        const inventories = await this._repo.getInventoriesForUser(
            userId,
            selectedWebsiteIds,
            parsedQuery,
        );

        getInventoriesLog.inc();
        Logger.info("get all inventories", {
            userId,
            action: "getInventories",
        });

        return getInventoriesSerializer(inventories);
    };

    editInventory = async (
        args: InventoryItemsDto,
        profile: Express.User,
        id: number,
    ) => {
        const { id: userId } = profile;

        // Normalize inventory data
        const normalizedArgs = normalizeData(args, {
            type: 'object',
            fieldOptions: {
                name: { type: 'text' },
                price: { type: 'price' },
                cost: { type: 'price' },
                total: { type: 'number' },
                reserved: { type: 'number' },
                url: { type: 'url' },
                attributes: { type: 'array' }
            }
        });
        const { categoryId, attributes, ...inventory } = normalizedArgs;

        // Check if attributes is null, undefined, or empty array
        if (utils.isNotNil(attributes) && attributes.length === 0) {
            Logger.error("Invalid attributes provided", {
                userId,
                action: "editInventory",
                statusCode: 400,
                error: "Empty attributes array is not allowed",
            });
            throw new errors.BadRequestError();
        }

        const existingInventory = await this.getInventoryOfUser(id, userId);

        let category = null;
        if (utils.isNotNil(categoryId)) {
            category = await this._category.getCategoryOfUser(
                categoryId,
                userId,
            );
        } else if (utils.isNotNil(existingInventory.categoryId)) {
            category = await this._category.getCategoryOfUser(
                existingInventory.categoryId,
                userId,
            );
        }

        // Generate attributes vector for search
        const attributesVector = this._generateAttributesVector(
            category?.name ?? "",
            normalizedArgs.name,
            attributes,
        );

        await this._repo.runTransaction(async (manager) => {
            const isUpdated = await this._repo.updateOneByQuery(
                { id, userId },
                {
                    ...inventory,
                    ...(utils.isNotNil(categoryId) && { categoryId }),
                    ...(utils.isNotNil(attributes) && {
                        attributesVector,
                    }),
                },
                { manager },
            );

            if (!isUpdated) {
                inventoryNotFoundErrorLog.inc();
                Logger.error("inventory update failed", {
                    statusCode: 404,
                    userId,
                    error: "Inventory not found",
                });

                recordErrorValue("not found error", "Inventory not found");

                throw new errors.NotFoundError("Inventory");
            }

            if (utils.isNotNil(attributes) && attributes.length > 0) {
                await this._attributeService.resetAttributes(
                    id,
                    attributes,
                    manager,
                );
            }

            // Load the inventory with attributes
            const updatedInventory = await this._getInventoryWithAttributesInTransaction(
                id,
                userId,
                manager
            );

            // Index in Elasticsearch
            await this._indexInventoryInElastic(
                updatedInventory!,
                category?.name ?? "نامشخص",
            );
        });

        editInventoryLog.inc();
        Logger.info("inventory updated", {
            userId,
            action: "editInventory",
            statusCode: 200,
        });
    };

    deleteInventory = async (user: Express.User, id: number) => {
        const { id: userId } = user;

        await this._repo.runTransaction(async (manager) => {
            await this._attributeService.removeAttributes(id, manager);

            const idDeleted = await this._repo.deleteOneByQuery(
                { userId, id },
                { manager },
            );

            if (!idDeleted) {
                inventoryNotFoundErrorLog.inc();
                Logger.error("inventory not found", {
                    error: "Inventory not found",
                    userId,
                    statusCode: 404,
                    action: "deleteInventory",
                });
                throw new errors.NotFoundError("Inventory");
            }

            await this._vectorStore.delete(id.toString());
        });
    };

    //Deprecated
    searchInventory = async (
        userId: number,
        attributes: SearchInventoryDto,
    ) => {
        const rawInventories = await this._repo.searchInventory(
            userId,
            attributes,
        );

        Logger.info("inventories returned according to attributes", {
            statusCode: 200,
            action: "searchInventory",
        });
        return getMatchingInventoriesSerializer(rawInventories).slice(0, 5);
    };

    getInventory = async (user: Express.User, id: number) => {
        const { id: userId } = user;
        const inventory = await this.getInventoryOfUser(id, userId);
        return inventory;
    };

    /**
     * Get inventory with attributes included
     * This method fetches an inventory item and includes its attributes relation
     * @param user The user making the request
     * @param id The ID of the inventory item
     * @returns The inventory item with attributes included
     */
    getInventoryWithAttributes = async (user: Express.User, id: number) => {
        const { id: userId } = user;

        // Use the repository to fetch the inventory with attributes
        const inventory = await this._repo.findOneByQuery(
            { id, userId },
            ["attributes"] // Include the 'attributes' relation
        );

        if (!inventory) {
            inventoryNotFoundErrorLog.inc();
            Logger.error("Inventory not found", {
                userId,
                inventoryId: id,
                statusCode: 404,
                action: "getInventoryWithAttributes",
            });
            throw new errors.NotFoundError("Inventory");
        }

        return inventory;
    };

    /**
     * Get inventory with attributes included within a transaction
     * This method is used internally when we need to fetch inventory with attributes
     * inside a transaction
     * @param id The ID of the inventory item
     * @param userId The ID of the user
     * @param manager The EntityManager for the transaction
     * @returns The inventory item with attributes included
     */
    private _getInventoryWithAttributesInTransaction = async (
        id: number,
        userId: number,
        manager: EntityManager
    ) => {
        // Use the repository to fetch the inventory with attributes
        const inventory = await this._repo.findOneByQuery(
            { id, userId },
            ["attributes"], // Include the 'attributes' relation
            { manager }
        );

        if (!inventory) {
            inventoryNotFoundErrorLog.inc();
            Logger.error("Inventory not found in transaction", {
                userId,
                inventoryId: id,
                statusCode: 404,
                action: "_getInventoryWithAttributesInTransaction",
            });
            throw new errors.NotFoundError("Inventory");
        }

        return inventory;
    };

    getInventoriesOfUserByIds = async (userId: number, ids: number[]) => {
        const inventories = await this._repo.getInventoriesOfUserByIds(
            userId,
            ids,
        );
        if (inventories.length !== ids.length) {
            inventoryNotFoundErrorLog.inc();

            Logger.error("Inventory not found error", {
                userId,
                statusCode: 404,
                error: "not found error",
            });

            recordErrorValue("not found error", "Inventory not found");
            Logger.error("Inventory ids did not match", {
                statusCode: 404,
                error: "Inventory not found",
            });
            throw new errors.NotFoundError("Inventory");
        }

        return inventories;
    };

    editStock = async (inventories: EditStockDto[], manager: EntityManager) => {
        await this._repo.editStock(inventories, manager);
    };

    getInventoryOfUser = async (id: number, userId: number) => {
        const inventory = await this._repo.findOneByQuery({ id, userId });
        if (utils.isNil(inventory)) {
            inventoryNotFoundErrorLog.inc();
            Logger.error("not found error", {
                message: "Inventory not found",
                statusCode: 404,
                userId,
                action: "getInventoryOfUser",
            });
            throw new errors.NotFoundError("Inventory");
        }

        return inventory;
    };

    getInventoryImage = async (id: number, userId?: number) => {
        Logger.info("Getting inventory image", {
            action: "getInventoryImage",
            inventoryId: id,
            requestingUserId: userId,
        });

        // If userId is provided, check ownership; otherwise use the old behavior for backward compatibility
        let inventory;
        if (userId) {
            inventory = await this._repo.findOneByQuery({ id, userId });
            if (utils.isNil(inventory)) {
                Logger.warn("Inventory not found for this user", {
                    action: "getInventoryImage",
                    inventoryId: id,
                    requestingUserId: userId,
                });
                // Try to find the inventory without user restriction to see if it exists at all
                const anyInventory = await this._repo.findById(id);
                if (anyInventory) {
                    Logger.warn("Inventory exists but belongs to different user", {
                        action: "getInventoryImage",
                        inventoryId: id,
                        requestingUserId: userId,
                        actualUserId: anyInventory.userId,
                        inventoryName: anyInventory.name,
                    });
                }
            }
        } else {
            inventory = await this._repo.findById(id);
        }

        if (utils.isNil(inventory)) {
            inventoryNotFoundErrorLog.inc();
            Logger.error("Inventory not found for image request", {
                message: "Inventory not found",
                statusCode: 404,
                action: "getInventoryImage",
                inventoryId: id,
                requestingUserId: userId,
            });
            throw new errors.NotFoundError("Inventory");
        }

        Logger.info("Found inventory for image", {
            action: "getInventoryImage",
            inventoryId: id,
            inventoryName: inventory.name,
            inventoryUserId: inventory.userId,
            requestingUserId: userId,
            imageArray: inventory.image,
            imageCount: inventory.image?.length || 0,
            firstImage: inventory.image?.[0] || null,
        });

        const imageUrl = inventory.image?.[0];
        if (!imageUrl) {
            Logger.warn("No image found for inventory", {
                action: "getInventoryImage",
                inventoryId: id,
                inventoryName: inventory.name,
                inventoryUserId: inventory.userId,
                requestingUserId: userId,
            });
        }

        return imageUrl;
    };


}
