import { MigrationInterface, QueryRunner } from "typeorm";

export class AddChatbotEnabledToChannel implements MigrationInterface {
    name = "AddChatbotEnabledToChannel";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "chatbotEnabled" boolean NOT NULL DEFAULT true`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" DROP COLUMN "chatbotEnabled"`,
        );
    }
}
